# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.orig

# C extensions
*.so

# Distribution / packaging
.Python
.pytest_cache/
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
# *.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# virtualenv
.venv
venv/
ENV/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# IDE
.vscode/
.idea
.gitpod.yml

# Tentacles manager temporary files
octobot/creator_temp/
creator_temp/

# Data
backtesting/collector/data/
backtesting/data/
report.html

# Tentacles
tentacles
downloaded_temp_tentacles

# User config
user/
temp_config.json

*.csv
*.ods
*.c
*.h

# OctoBot logs
logs

# Debug
cython_debug/

# env
.env
.env.local
