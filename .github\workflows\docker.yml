name: Triangular Arbitrage - Docker
on:
  push:
    branches:
      - "master"
    tags:
      - "*"
  pull_request:

jobs:
  lint:
    name: ubuntu-latest - Docker - lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Run hadolint
        uses: reviewdog/action-hadolint@v1
        with:
          github_token: ${{ secrets.github_token }}
          hadolint_ignore: DL3013 DL3008

  build:
    needs: lint
    name: ubuntu-latest - Docker - build & test & push
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Set Environment Variables
        run: |
          OWNER="$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]' | tr -d '-')"
          IMG=octobot-triangular-arbitrage
          echo "VERSION=${GITHUB_REF##*/}" >> $GITHUB_ENV
          echo "IMAGE=${OWNER}/${IMG}" >> $GITHUB_ENV
          echo "LATEST=latest" >> $GITHUB_ENV

      - name: Set up QEMU
        id: qemu-setup
        uses: docker/setup-qemu-action@master
        with:
          platforms: all

      - name: Print available platforms
        run: echo ${{ steps.qemu.outputs.platforms }}

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@master
        with:
          driver: docker-container
          use: true

      - name: Cache Docker layers
        uses: actions/cache@v2
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to DockerHub
        if: github.event_name == 'push'
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push latest
        if: github.event_name == 'push' && !startsWith(github.ref, 'refs/tags') && github.ref == 'refs/heads/master'
        uses: docker/build-push-action@master
        with:
          context: .
          builder: ${{ steps.buildx.outputs.name }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.IMAGE }}:${{ env.LATEST }}
          build-args: |
            TENTACLES_URL_TAG=${{ env.LATEST }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Build and push on tag
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags')
        uses: docker/build-push-action@master
        with:
          context: .
          file: ./Dockerfile
          builder: ${{ steps.buildx.outputs.name }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ env.IMAGE }}:${{ env.LATEST }}
            ${{ env.IMAGE }}:${{ env.VERSION }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

  notify:
    if: ${{ failure() }}
    needs:
      - lint
      - build
    uses: Drakkar-Software/.github/.github/workflows/failure_notify_workflow.yml@master
    secrets:
      DISCORD_GITHUB_WEBHOOK: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}