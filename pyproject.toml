[project]
name = "triangular-arbitrage"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.12"
dependencies = [
    "ccxt>=4.5.2",
    "networkx[default]>=3.5",
    "octobot-commons>=1.9,<1.10",
]

[dependency-groups]
dev = [
    "coverage>=7.10.6",
    "pip>=25.2",
    "pylint>=3.3.8",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pytest-pep8>=1.0.6",
    "setuptools>=80.9.0",
    "twine>=6.1.0",
    "wheel>=0.45.1",
]
