name: Triangular Arbitrage - CI
on:
  push:
    branches:
      - 'master'
    tags:
      - '*'
  pull_request:

jobs:
  lint:
    uses: <PERSON><PERSON>kar-Software/.github/.github/workflows/python3_lint_workflow.yml@master
    with:
      project_main_package: triangular_arbitrage

  tests:
    needs: lint
    uses: Drakkar-Software/.github/.github/workflows/python3_tests_workflow.yml@master

  publish:
    needs: tests
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags')
    uses: Dr<PERSON><PERSON>-Software/.github/.github/workflows/python3_sdist_workflow.yml@master
    secrets:
      PYPI_OFFICIAL_UPLOAD_URL: ${{ secrets.PYPI_OFFICIAL_UPLOAD_URL }}
      PYPI_USERNAME: __token__
      PYPI_PASSWORD: ${{ secrets.PYPI_TOKEN }}

  notify:
    if: ${{ failure() }}
    needs:
      - lint
      - tests
      - publish
    uses: <PERSON><PERSON><PERSON>-Software/.github/.github/workflows/failure_notify_workflow.yml@master
    secrets:
      DISCORD_GITHUB_WEBHOOK: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}